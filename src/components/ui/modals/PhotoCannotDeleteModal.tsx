'use client'

import { useRef, useEffect } from 'react'
import { useLanguage } from '@/context/LanguageContext'
import { useTheme, useThemeConfig } from '@/context/ThemeContext'
import { Button } from '@/components/ui'
import { FaExclamationTriangle, FaTimes } from 'react-icons/fa'

interface Dependency {
  type?: 'faq' | 'welcome'
  id?: number
  question_p?: string
  question?: string
  chat_id?: string
  answer_p?: string
}

interface PhotoCannotDeleteModalProps {
  showCannotDeleteModal: boolean
  dependencies: (Dependency | string)[] // Support both new format and old string format
  onClose: () => void
}

export default function PhotoCannotDeleteModal({
  showCannotDeleteModal,
  dependencies,
  onClose
}: PhotoCannotDeleteModalProps) {
  const { t } = useLanguage()
  const { theme } = useTheme()
  const themeConfig = useThemeConfig()
  const cannotDeleteModalRef = useRef<HTMLDivElement>(null)

  // Handle escape key to close modal
  useEffect(() => {
    if (showCannotDeleteModal) {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
          event.preventDefault()
          onClose()
        }
      }
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [showCannotDeleteModal, onClose])

  if (!showCannotDeleteModal) return null

  return (
    <div
      className={`fixed inset-0 bg-black/20 backdrop-blur-sm flex items-center justify-center z-50`}
    >
      <div
        ref={cannotDeleteModalRef}
        className={`relative rounded-2xl p-6 w-full max-w-md mx-4 border-2 overflow-hidden ${theme === 'light' ? 'bg-white border-gray-300' : 'bg-red-900/[0.8] border-red-500/50'}`}
        onClick={(e: React.MouseEvent) => e.stopPropagation()}
      >
        {theme === 'dark' && (
          <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-transparent opacity-50 rounded-2xl"></div>
        )}
        <div className="relative z-10">
          {/* Close button (X) */}
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 h-7 w-7 rounded-full bg-black/20 hover:bg-white/20 text-white/70 hover:text-white"
            onClick={onClose}
            aria-label="Close"
          >
            <FaTimes />
          </Button>
          <div className="flex justify-center mb-4">
            <FaExclamationTriangle className="w-6 h-6 text-red-600" />
          </div>
          <h3 className={`text-xl font-bold mb-4 font-title text-center ${themeConfig.text}`}>
            {t('cannot_delete_photo')}
          </h3>
          <p className={`${themeConfig.textSecondary} mb-4 text-center`}>
            {dependencies.some(dep => typeof dep === 'object' && dep.type === 'welcome') ? t('photo_in_use_welcome') : t('photo_in_use')}
          </p>
          {dependencies.length > 0 && (
            <div className={`mb-6 ${theme === 'light' ? 'bg-white' : 'bg-black/30'} rounded-lg p-3 max-h-40 overflow-y-auto`}>
              {/* Handle old format (array of strings) */}
              {dependencies.some(dep => typeof dep === 'string') && (
                <div className="mb-3">
                  <p className={`${themeConfig.textMuted} text-sm mb-2`}>{t('linked_questions')}:</p>
                  <ul className={`list-disc pl-5 ${themeConfig.textSecondary} text-sm`}>
                    {dependencies
                      .filter(dep => typeof dep === 'string')
                      .map((question, index) => (
                        <li key={`legacy-${index}`} className="mb-1">
                          {question}
                        </li>
                      ))}
                  </ul>
                </div>
              )}

              {/* Handle new format - FAQ Dependencies */}
              {dependencies.filter(dep => typeof dep === 'object' && dep.type === 'faq').length > 0 && (
                <div className="mb-3">
                  <p className={`${themeConfig.textMuted} text-sm mb-2`}>{t('linked_questions')}:</p>
                  <ul className={`list-disc pl-5 ${themeConfig.textSecondary} text-sm`}>
                    {dependencies
                      .filter(dep => typeof dep === 'object' && dep.type === 'faq')
                      .map((dep, index) => (
                        <li key={`faq-${index}`} className="mb-1">
                          {typeof dep === 'object' && dep.question_p && dep.question_p.length > 30
                            ? `${dep.question_p.substring(0, 30)}...`
                            : (typeof dep === 'object' ? (dep.question_p || dep.question || 'Untitled question') : 'Untitled question')}
                        </li>
                      ))}
                  </ul>
                </div>
              )}

              {/* Handle new format - Welcome Dependencies */}
              {dependencies.filter(dep => typeof dep === 'object' && dep.type === 'welcome').length > 0 && (
                <div>
                  <p className={`${themeConfig.textMuted} text-sm mb-2`}>{t('linked_welcome')}:</p>
                  <ul className={`list-disc pl-5 ${themeConfig.textSecondary} text-sm`}>
                    {dependencies
                      .filter(dep => typeof dep === 'object' && dep.type === 'welcome')
                      .map((dep, index) => (
                        <li key={`welcome-${index}`} className="mb-1">
                          Welcome page intro message
                        </li>
                      ))}
                  </ul>
                </div>
              )}
            </div>
          )}
          <div className="flex justify-center">
            <Button
              onClick={onClose}
              variant="cancel"
              size="md"
              className="px-6"
            >
              {t('close')}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}