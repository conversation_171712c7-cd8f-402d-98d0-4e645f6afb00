import { NextRequest, NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { generateWebhookToken } from '@/utils/jwt'
import { v4 as uuidv4 } from 'uuid'
import { getM4AExtension } from '@/utils/audioConversion'
import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3'
import { serverCache } from '@/lib/cache'
import { queryOne, query } from '@/lib/postgres'

// Validate R2 environment variables
const validateR2Config = () => {
  const requiredEnvVars = [
    'CLOUDFLARE_R2_ENDPOINT',
    'CLOUDFLARE_R2_ACCESS_KEY_ID',
    'CLOUDFLARE_R2_SECRET_ACCESS_KEY',
    'CLOUDFLARE_R2_BUCKET_NAME',
    'CLOUDFLARE_R2_PUBLIC_URL'
  ]

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Missing required environment variable: ${envVar}`)
    }
  }
}

// Validate configuration on module load
validateR2Config()

// Initialize R2 client with proper configuration for Cloudflare R2
const r2Client = new S3Client({
  region: 'auto', // R2 uses 'auto' region
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT!,
  forcePathStyle: true, // Required for R2 compatibility
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY!,
  },
})

const BUCKET_NAME = process.env.CLOUDFLARE_R2_BUCKET_NAME!
const PUBLIC_URL = process.env.CLOUDFLARE_R2_PUBLIC_URL!

// Helper function to get content type
function getContentType(fileName: string): string {
  const extension = fileName.split('.').pop()?.toLowerCase()
  switch (extension) {
    case 'm4a':
      return 'audio/mp4'
    case 'mp3':
      return 'audio/mpeg'
    case 'wav':
      return 'audio/wav'
    case 'ogg':
      return 'audio/ogg'
    default:
      return 'audio/mp4'
  }
}

// No interface needed - using the old updateData pattern

// GET - Fetch welcome chat data for the client
export async function GET() {
  try {
    // Verify authentication
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Validate clientId exists
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    const chatId = `${clientId}-1`

    // Fetch welcome chat data directly from PostgreSQL
    const sql = `
      SELECT chat_id, answer_p, audio_url, audio_file_path, photo_url, photo_id
      FROM welcome_chat
      WHERE client_id = $1 AND chat_id = $2
      ORDER BY created_at DESC
      LIMIT 1
    `

    const rawRecord = await queryOne(sql, [clientId, chatId])

    // Handle case where no record exists
    if (!rawRecord) {
      return NextResponse.json({
        success: true,
        body: [],
        error_msg: null
      })
    }
    
    // Process the single record
    const record = rawRecord as Record<string, unknown>
    
    // Check if audio exists (has audio_url)
    const hasAudio = record.audio_url && typeof record.audio_url === 'string' && record.audio_url.trim() !== ''
    
    // Check if photo exists (has photo_url and photo_id)
    const hasPhoto = record.photo_url && record.photo_id && 
                    Array.isArray(record.photo_url) && record.photo_url.length > 0 && 
                    typeof record.photo_id === 'string' && record.photo_id.trim() !== ''
    
    const processedRecord = {
      chat_id: record.chat_id,
      answer_p: hasAudio ? '' : (record.answer_p || ''), // Clear text if audio mode
      audio_url: record.audio_url || null,
      audio_file_path: record.audio_file_path || null,
      audioCode: hasAudio ? 'existing_audio' : '', // Set to empty if no audio
      photo_url: hasPhoto ? record.photo_url : [], // Set to empty array if no photo
      photo_id: hasPhoto ? record.photo_id : '' // Set to empty string if no photo
    }
    
    // Return as array to match frontend expectations
    const processedWelcomeData = [processedRecord]

    return NextResponse.json({ 
      success: true,
      body: processedWelcomeData,
      error_msg: null
    })

  } catch (error: unknown) {
    console.error('Error in welcome chat GET API:', error)
    return NextResponse.json(
      { 
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// PUT - Update welcome chat data
export async function PUT(request: NextRequest) {
  try {
    // Verify authentication
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Validate clientId exists
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Get webhook URL for background analytics (optional)
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL

    // Parse request body - handle both FormData (with audio) and JSON
    let chat_id: string, updateData: any, sectorFromRequest: string | null = null, langFromRequest: string | null = null;
    let audioBlob: Blob | null = null;

    const contentType = request.headers.get('content-type') || '';

    if (contentType.includes('multipart/form-data')) {
      // Handle FormData (with audio blob)
      const formData = await request.formData();
      chat_id = formData.get('chat_id') as string;
      updateData = JSON.parse(formData.get('updateData') as string);
      sectorFromRequest = formData.get('sector') as string | null;
      langFromRequest = formData.get('lang') as string | null;
      audioBlob = formData.get('audioBlob') as Blob | null;
    } else {
      // Handle JSON (no audio)
      const body = await request.json();
      chat_id = body.chat_id;
      updateData = body.updateData;
      sectorFromRequest = body.sector || null;
      langFromRequest = body.lang || null;
    }

    if (!chat_id || !updateData) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Missing required fields: chat_id, updateData'
      }, { status: 400 })
    }

    // Use client info from frontend request (with fallbacks like add-batch)
    let clientInfo = null

    if (sectorFromRequest || langFromRequest) {
      // Use sector/lang passed from frontend
      clientInfo = {
        sector: sectorFromRequest,
        lang: langFromRequest
      }
    } else {
      // Fallback 1: Try knowledge cache
      const knowledgeCacheKey = `knowledge_${authId}`
      const knowledgeData = serverCache.get(knowledgeCacheKey)

      if (knowledgeData) {
        clientInfo = {
          sector: knowledgeData.sector,
          lang: knowledgeData.clientLang
        }
      } else {
        // Fallback 2: Get client info from database
        try {
          const clientInfoSql = `
            SELECT sector, lang
            FROM clients
            WHERE auth_id = $1
          `
          const clientData = await queryOne(clientInfoSql, [authId])
          if (clientData) {
            clientInfo = {
              sector: clientData.sector,
              lang: clientData.lang
            }
          }
        } catch (error) {
          console.warn('Could not get client info from database:', error)
        }
      }
    }

    // Ensure we have sector and lang for the webhook
    if (!clientInfo?.sector || !clientInfo?.lang) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Could not determine sector and language for welcome chat update'
      }, { status: 400 })
    }

    // Handle audio processing with blob upload (like lists API)
    if (audioBlob && updateData.answerChanged && updateData.answer_p === null) {

      try {
        // Convert blob to buffer for R2 upload
        const processedAudioBlob = audioBlob;
        const audioBuffer = Buffer.from(await processedAudioBlob.arrayBuffer());

        // Generate audio ID and file path with timestamp+UUID
        const timestamp = Date.now()
        const uniqueId = uuidv4()
        const audioId = `${timestamp}-${uniqueId}`
        const fileExtension = getM4AExtension()
        const fileName = `${audioId}.${fileExtension}`
        const filePath = `audios/${authId}/${fileName}`

        // Direct R2 upload - optimized to not use /api/file/audios
        const contentType = getContentType(fileName)

        const putCommand = new PutObjectCommand({
          Bucket: BUCKET_NAME,
          Key: filePath,
          Body: new Uint8Array(audioBuffer),
          ContentType: contentType,
          CacheControl: '3600',
          ContentDisposition: `inline; filename="${fileName}"`,
        })

        await r2Client.send(putCommand)

        // Generate public URL
        const publicUrl = `${PUBLIC_URL}/${filePath}`

        // Update the updateData with audio information
        updateData.audio_url = publicUrl
        updateData.audio_file_path = filePath

        // Fire off old audio deletion asynchronously (don't wait for it) - direct R2 deletion
        if (updateData.originalAudioFilePath) {
          const deleteCommand = new DeleteObjectCommand({
            Bucket: BUCKET_NAME,
            Key: updateData.originalAudioFilePath,
          })

          r2Client.send(deleteCommand).catch(cleanupError => {
            console.error('Background cleanup failed for old intro audio file:', updateData.originalAudioFilePath, cleanupError)
            // Silently fail - this won't affect the user experience
          })
        }

      } catch (audioError: unknown) {
        console.error('Error processing audio:', audioError)
        return NextResponse.json({
          success: false,
          body: null,
          error_msg: `Failed to process audio: ${audioError instanceof Error ? audioError.message : 'Unknown error'}`
        }, { status: 500 })
      }
    } else if (updateData.originalAudioFilePath && updateData.answerChanged && updateData.audio_url === null) {
      // Handle cleanup when switching from audio to text mode - direct R2 deletion
      try {
        const deleteCommand = new DeleteObjectCommand({
          Bucket: BUCKET_NAME,
          Key: updateData.originalAudioFilePath,
        })

        await r2Client.send(deleteCommand)

        // Clear audio-related fields
        updateData.audio_url = null
        updateData.audio_file_path = null
      } catch (cleanupError) {
        console.error('Error during audio cleanup when switching to text mode:', cleanupError)
        // Don't fail the operation if cleanup fails
      }
    }

    // Get current welcome chat for cleanup if needed
    let currentRecord = null
    if (updateData.answerChanged) {
      const currentRecordSql = `
        SELECT audio_url, audio_file_path
        FROM welcome_chat
        WHERE client_id = $1 AND chat_id = $2
      `
      currentRecord = await queryOne(currentRecordSql, [clientId, chat_id])
    }

    // Clean up old audio file if answer changed and old audio exists (like knowledgebase)
    if (updateData.answerChanged && currentRecord?.audio_url && currentRecord?.audio_file_path) {
      try {
        const deleteCommand = new DeleteObjectCommand({
          Bucket: BUCKET_NAME,
          Key: currentRecord.audio_file_path,
        })
        
        await r2Client.send(deleteCommand)
      } catch (cleanupError) {
        console.error('Error cleaning up old intro audio file:', cleanupError)
        // Don't fail the operation if cleanup fails
      }
    }

    // Handle database updates based on change flags
    const dbUpdateData: Record<string, unknown> = {}

    if (updateData.photoChanged) {
      dbUpdateData.photo_url = updateData.photo_url
      dbUpdateData.photo_id = updateData.photo_id
      dbUpdateData.fb_photo_atmid = null
      dbUpdateData.tg_photo_atmid = null
      dbUpdateData.ig_photo_atmid = null
    }

    if (updateData.answerChanged) {
      dbUpdateData.answer_p = updateData.answer_p
      dbUpdateData.audio_url = updateData.audio_url
      dbUpdateData.audio_duration = updateData.audio_duration
      dbUpdateData.fb_audio_atmid = null
      dbUpdateData.tg_audio_atmid = null
      dbUpdateData.ig_audio_atmid = null
      if (updateData.audio_file_path) {
        dbUpdateData.audio_file_path = updateData.audio_file_path
      }
    }

    // Prepare update fields and values for PostgreSQL
    const updateFields: string[] = []
    const updateValues: unknown[] = []
    let valueIndex = 3

    // Build dynamic update query based on changed fields
    Object.entries(dbUpdateData).forEach(([key, value]) => {
      updateFields.push(`${key} = $${valueIndex}`)
      updateValues.push(value)
      valueIndex++
    })

    if (updateFields.length === 0) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'No fields to update'
      }, { status: 400 })
    }

    // Update welcome chat directly in PostgreSQL
    const updateSql = `
      UPDATE welcome_chat
      SET ${updateFields.join(', ')}
      WHERE client_id = $1 AND chat_id = $2
    `

    const result = await query(updateSql, [clientId, chat_id, ...updateValues])

    if (result.rowCount === 0) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Welcome chat not found or update failed'
      }, { status: 404 })
    }

    // Fire-and-forget webhook for analytics (background only) - only for answer changes
    if (webhookUrl && updateData.answerChanged) {
      const jwtToken = generateWebhookToken()

      fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${jwtToken}`
        },
        body: JSON.stringify({
          mode: 'welcome',
          operation: 'update',
          conversation_id: uuidv4(), // Generated UUID for conversation tracking
          chat_id: chat_id,
          client_id: clientId,
          answer: updateData.answer_p || null,
          audio_url: updateData.audio_url || null,
          sector: clientInfo.sector || null,
          lang: clientInfo.lang || null
        })
      }).catch(error => {
        console.warn('Fire-and-forget webhook failed:', error)
      })
    }

    // No cache update needed - welcome data is fetched fresh each time
    // Frontend handles optimistic UI updates with local state

    // Return the new audio URL if audio was processed
    const responseBody = updateData.answerChanged && updateData.audio_url ? {
      audio_url: updateData.audio_url,
      audio_file_path: updateData.audio_file_path
    } : null

    return NextResponse.json({
      success: true,
      body: responseBody,
      error_msg: null
    })

  } catch (error: unknown) {
    console.error('Error in welcome chat PUT API:', error)
    return NextResponse.json(
      { 
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}