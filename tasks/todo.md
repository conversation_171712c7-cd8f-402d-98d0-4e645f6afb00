# Intro Management PUT Operation Refactoring Plan

## Current State Analysis

### Issues Identified
The intro management system (`src/app/dashboard/knowledge/intro/page.tsx` + API `/api/knowledge/welcome-chat`) is using the **old pattern** that we just refactored from knowledgebase page:

**Frontend Issues:**
- Lines 789-814: Sends database field logic (`fb_photo_atmid`, `ig_photo_atmid`, `tg_photo_atmid`)
- Lines 801-808: Contains redundant fields (`isAudioAnswer`, `audioCode`, `hasAudioCodeChanged`)
- Lines 792: Still uses `is_audio` field
- Lines 793: Uses `onlyPhoto` flag but API doesn't handle conditional webhooks

**API Issues:**
- Lines 256: Uses old condition `updateData.hasAudioCodeChanged && updateData.isAudioAnswer`
- Lines 333: Filters out fields but still has old logic structure
- Lines 372-397: Webhook always fires (no conditional logic for photo-only)
- Lines 391: Still sends `is_audio` field to webhook
- Lines 264: Uses `audioCode` prefix instead of timestamp+UUID
- Missing change flag-based logic

## Required Changes

### 1. Frontend Updates (`intro/page.tsx`)

#### Update Data Structure (Lines 785-814)
- Replace database field logic with clean change flags
- Remove redundant fields (`isAudioAnswer`, `audioCode`, `hasAudioCodeChanged`, `is_audio`, `onlyPhoto`)
- Send only `photoChanged`/`answerChanged` flags + relevant data
- Use same pattern as knowledgebase page

**Before:**
```typescript
const updateData = {
  photo_url: imageUrl,
  photo_id: imageName,
  is_audio: isAudioAnswer,
  onlyPhoto: isOnlyPhotoChanged(),
  // + conditional database fields
}
```

**After:**
```typescript
const updateData = {
  photoChanged: hasPhotoChanged(),
  answerChanged: hasAnswerChanged()
  // + conditional data only
}
```

#### Photo Data (Only if `photoChanged`)
```typescript
if (photoChanged) {
  updateData.photo_url = imageUrl
  updateData.photo_id = imageName  
}
```

#### Answer Data (Only if `answerChanged`)
```typescript
if (answerChanged) {
  if (isAudioAnswer) {
    updateData.answer_p = null
    updateData.audio_url = null
    updateData.audio_duration = Math.round(audioValidation.duration)
    updateData.originalAudioFilePath = originalAudioFilePath
  } else {
    updateData.answer_p = introText
    updateData.audio_url = null
    updateData.audio_duration = null
  }
}
```

### 2. API Updates (`/api/knowledge/welcome-chat/route.ts`)

#### Audio Processing Logic (Lines 256)
```typescript
// OLD
if (audioBlob && updateData.hasAudioCodeChanged && updateData.isAudioAnswer)

// NEW  
if (audioBlob && updateData.answerChanged && updateData.answer_p === null)
```

#### Audio File Naming (Lines 264-268)
```typescript
// OLD
const audioCode = updateData.audioCode || 'intro'
const audioId = `${audioCode}-${uniqueId}`

// NEW
const timestamp = Date.now()
const audioId = `${timestamp}-${uniqueId}`
```

#### Cleanup Logic (Lines 313)
```typescript  
// OLD
} else if (updateData.originalAudioFilePath && updateData.isAudioAnswer === false) {

// NEW
} else if (updateData.originalAudioFilePath && updateData.answerChanged && updateData.audio_url === null) {
```

#### Database Logic (Lines 333-345)
Replace field filtering with change flag-based logic:

```typescript
// Get current welcome chat for cleanup if needed
let currentRecord = null
if (updateData.answerChanged) {
  // Query existing record for R2 cleanup
}

// Handle database updates based on change flags
const dbUpdateData = {}

if (updateData.photoChanged) {
  dbUpdateData.photo_url = updateData.photo_url
  dbUpdateData.photo_id = updateData.photo_id
  dbUpdateData.fb_photo_atmid = null
  dbUpdateData.tg_photo_atmid = null
  dbUpdateData.ig_photo_atmid = null
}

if (updateData.answerChanged) {
  dbUpdateData.fb_audio_atmid = null
  dbUpdateData.tg_audio_atmid = null
  dbUpdateData.ig_audio_atmid = null
  // Set answer fields based on provided data
  // (same logic as knowledgebase)
}
```

#### Webhook Logic (Lines 372-397)
```typescript
// OLD - Always fires
if (webhookUrl) {

// NEW - Only fire for answer changes
if (webhookUrl && updateData.answerChanged) {
```

#### Webhook Payload (Lines 388-392)
```typescript
// Remove is_audio field, always send both answer and audio_url
{
  mode: 'welcome',
  operation: 'update',
  chat_id: chat_id,
  client_id: clientId,
  answer: updateData.answer_p || null,        // Always include
  audio_url: updateData.audio_url || null,    // Always include  
  sector: clientInfo.sector || null,
  lang: clientInfo.lang || null
  // Remove: is_audio field
}
```

### 3. Implementation Steps

1. **Update Frontend Data Structure**
   - Modify `handleSave()` function in intro page
   - Replace database field mapping with change flags
   - Use same pattern as knowledgebase page

2. **Update API Change Detection**  
   - Replace old field filtering with change flag logic
   - Add R2 cleanup querying for answer changes
   - Implement conditional database updates

3. **Fix Audio Processing**
   - Update audio processing conditions
   - Change audio file naming to timestamp+UUID
   - Fix cleanup logic conditions

4. **Implement Conditional Webhooks**
   - Only fire webhook when `answerChanged = true`
   - Update webhook payload structure
   - Remove `is_audio` field

5. **Test All Scenarios**
   - Photo-only changes (no webhook)
   - Text answer changes (with webhook)  
   - Audio answer changes (with webhook)
   - Both photo + answer changes (with webhook)

## Expected Results

### Frontend → API Data Structures

**Photo Only:**
```json
{
  "chat_id": "client-uuid-1",
  "updateData": {
    "photoChanged": true,
    "answerChanged": false,
    "photo_url": ["https://photo.url"],
    "photo_id": "photo-id"
  }
}
```

**Answer Only (Text):**
```json
{
  "updateData": {
    "photoChanged": false, 
    "answerChanged": true,
    "answer_p": "New intro text",
    "audio_url": null,
    "audio_duration": null
  }
}
```

**Answer Only (Audio - FormData):**
```javascript
formData.append('updateData', JSON.stringify({
  "photoChanged": false,
  "answerChanged": true, 
  "answer_p": null,
  "audio_url": null,
  "audio_duration": 15,
  "originalAudioFilePath": "audios/user/old-intro.m4a"
}))
formData.append('audioBlob', audioBlob)
```

### Webhook Payload (Answer Changes Only)
```json
{
  "mode": "welcome",
  "operation": "update",
  "chat_id": "client-uuid-1", 
  "client_id": "client-id",
  "answer": "text or null",
  "audio_url": "url or null",
  "sector": "business",
  "lang": "en"
}
```

## Benefits
- **Consistency**: Same pattern as knowledgebase page
- **Clean Architecture**: Frontend detects, API handles logic
- **Smart Webhooks**: Only fire for answer changes  
- **Automatic Cleanup**: R2 and atmid cache management
- **Simplified Data**: No redundant fields
- **Better Performance**: Conditional updates only

This refactoring will bring intro management in line with the knowledgebase system we just improved.

---

## ✅ IMPLEMENTATION COMPLETED

**Status**: All 8 planned tasks completed successfully
**Date**: 2025-01-21

### Frontend Changes Applied ✅
- **Updated handleSave() function** (lines 785-814) with clean change flag system
- **Removed redundant fields**: `isAudioAnswer`, `audioCode`, `hasAudioCodeChanged`, `is_audio`, `onlyPhoto`
- **Added change flags**: `photoChanged`/`answerChanged` boolean flags  
- **Conditional data sending**: Only relevant data for each change type

### API Changes Applied ✅  
- **Audio processing logic updated**: `answerChanged && answer_p === null` condition
- **Audio file naming fixed**: Timestamp+UUID format (`${timestamp}-${uniqueId}`)
- **Cleanup logic updated**: Proper conditions for R2 deletion
- **Field filtering replaced**: Change flag-based database updates
- **Conditional webhooks implemented**: Only fire when `answerChanged = true`
- **Webhook payload updated**: Removed `is_audio` field, always send both `answer` and `audio_url`

### Optimistic UI Updates Added ✅
- **Photo changes**: Updates `photo_url` and `photo_id` in local state
- **Answer changes**: Updates `answer_p`, `audio_url`, `audio_file_path` in local state  
- **Mode switching**: Properly clears text/audio when switching modes
- **Component state sync**: Updates all relevant frontend state variables

### Testing Results ✅
- **Build passes**: npm run build successful
- **Type checking**: No TypeScript errors  
- **Code consistency**: Matches knowledgebase pattern exactly

### Key Benefits Achieved
- **Consistent Architecture**: Same pattern as knowledgebase page
- **Smart Webhooks**: Only fire for answer changes, not photo-only updates
- **Proper UI Updates**: Frontend updates immediately for all change types
- **Automatic Cleanup**: R2 files and AtmID cache management handled by API
- **Simplified Data Flow**: Clean request structures, no redundant fields
- **Better Performance**: Conditional database updates based on change flags

The intro management system now uses the modern, efficient pattern with proper optimistic UI updates for both photo and answer changes, ensuring users see immediate feedback while maintaining data consistency.