# Intro Management System

Manages intro (welcome chat) messages with text, audio, and photo content using modern change flag architecture.

## System Overview

### Core Features
- **Text/Audio Intro Messages**: Create and edit with seamless mode switching
- **Photo Attachment**: Search and attach photos from knowledge cache
- **Change Flag Architecture**: Clean separation of concerns with boolean change detection
- **Smart Webhooks**: Conditional webhook firing based on change type
- **R2 Storage Management**: Direct audio upload with comprehensive cleanup
- **Optimistic UI Updates**: Immediate feedback with proper state synchronization

### Architecture
- **Primary Database**: Direct PostgreSQL with connection pooling
- **Data Loading**: Mixed strategy - knowledge cache for stats, fresh fetch for intro data
- **Single Record**: One intro per client in `welcome_chat` table
- **Cache Integration**: Uses knowledge cache for client metadata with fallback chain
- **API Endpoint**: `/api/knowledge/welcome-chat` for CRUD operations
- **Webhooks**: Conditional fire-and-forget for answer changes only

## Change Flag Architecture

### Modern Change Detection
**Philosophy**: Frontend detects changes, API handles database logic
**Implementation**: Clean boolean flags with conditional data sending
**Consistency**: Matches knowledgebase system architecture exactly

### Change Flag System
1. **`photoChanged`**: Boolean flag indicating photo updates
2. **`answerChanged`**: Boolean flag indicating answer/audio updates
3. **Conditional Data**: Only send relevant data when flags are true
4. **Clean Separation**: Frontend detection, API database management

### Benefits
- **Clean Architecture**: Clear separation of concerns
- **Smart Webhooks**: Only fire for answer changes, not photo-only updates
- **Efficient Updates**: Conditional database operations based on change flags
- **Automatic Cleanup**: R2 file deletion and AtmID cache invalidation handled by API
- **Simplified Data Flow**: No redundant fields, cleaner request structures

## Update Data Patterns

### Frontend → API Data Flow

**Photo Only Changes:**
```json
{
  "chat_id": "client-uuid-1",
  "updateData": {
    "photoChanged": true,
    "answerChanged": false,
    "photo_url": ["https://photo.url"],
    "photo_id": "photo-id"
  }
}
```

**Answer Only (Text):**
```json
{
  "updateData": {
    "photoChanged": false,
    "answerChanged": true,
    "answer_p": "New intro text",
    "audio_url": null,
    "audio_duration": null
  }
}
```

**Answer Only (Audio - FormData):**
```javascript
formData.append('updateData', JSON.stringify({
  "photoChanged": false,
  "answerChanged": true,
  "answer_p": null,
  "audio_url": null,
  "audio_duration": 15,
  "originalAudioFilePath": "audios/user/old-intro.m4a"
}))
formData.append('audioBlob', audioBlob)
```

### Backend Processing
- **Change Flag Logic**: Database updates based on `photoChanged`/`answerChanged` flags
- **Conditional Webhooks**: Only fire when `answerChanged = true`
- **Automatic AtmID Reset**: Clear platform cache IDs based on change type
- **R2 Cleanup**: Comprehensive audio file cleanup when switching modes
- **Dynamic SQL**: Build update query based on change flags

## Audio Processing

### Modern Audio Pipeline
1. **Validation**: Real-time audio validation with duration checking
2. **Blob Processing**: Direct audio blob processing from client
3. **Direct Upload**: Direct R2 upload using AWS S3 SDK (no API intermediary)
4. **File Naming**: Timestamp+UUID format: `${timestamp}-${uuid}.m4a`
5. **Public URL**: Generate Cloudflare R2 public URLs for playback
6. **Comprehensive Cleanup**: Dual cleanup mechanism for complete R2 storage management

### Audio Features
- **Real-time Validation**: Duration and format validation with instant feedback
- **Direct R2 Upload**: Bypasses API bottlenecks for better performance
- **Mode Switching**: Seamless transition between text and audio modes
- **Smart Cleanup**: Multiple cleanup mechanisms ensure no orphaned files

### R2 Storage Management
- **Dual Cleanup**: Frontend-provided path + database-queried current files
- **Audio-to-Text**: Automatic cleanup when switching from audio to text mode
- **Audio-to-Audio**: Replace old audio files when uploading new ones
- **Error Handling**: Graceful failure doesn't break update operations
- **Background Processing**: Asynchronous cleanup doesn't block user experience

## Photo Management

### Photo Integration
- **Shared System**: Uses same photo search as knowledge page
- **Cache-First**: Search local photos before API fallback
- **Attachment**: Single photo attachment per intro message
- **Gallery Support**: Store full URL array for modal display

### Photo Features
- **Search**: Local filtering with API fallback to `/api/knowledge/search-photos`
- **Selection**: Thumbnail display with full URL array storage
- **Clearing**: Reset selection and search state
- **ATM ID Management**: Clear platform-specific IDs when photo changes

## Data Loading Strategy

### Cache-First Pattern
- **Dashboard Cache**: Primary source for client_id, sector, lang
- **Selective Fallback**: Query database only for missing fields
- **Performance**: Reduces redundant database queries

### Data Sources
- **Knowledge Cache**: Statistics and photo data via `useKnowledgeData()`
- **Fresh Fetch**: Intro data fetched fresh each time (not cached)
- **Mixed Strategy**: Optimal balance between performance and freshness

## API Endpoints

### GET `/api/knowledge/welcome-chat` - Fetch Intro Data
**Purpose**: Retrieve single intro record per client
**Auth**: Required (verifyAuth)
**Database**: Direct PostgreSQL query to `welcome_chat` table
**Response**: Single record formatted as array for UI compatibility
**Processing**: Handles audio/photo existence detection

### PUT `/api/knowledge/welcome-chat` - Update Intro
**Purpose**: Update intro with change flag-based logic
**Auth**: Required (verifyAuth)
**Body**: `{ chat_id: string, updateData: object, sector?: string, lang?: string }`
**Database**: Direct PostgreSQL update to `welcome_chat` table
**Cache Fallback**: Frontend cache → Server cache → Database for sector/lang
**Features**:
- **Change Flag Logic**: `photoChanged`/`answerChanged` flags drive database updates
- **Conditional Webhook**: Only fires when `answerChanged = true` (no webhook for photo-only)
- **Direct R2 Upload**: Audio processing with timestamp+UUID naming
- **Dual R2 Cleanup**: Frontend-provided path + database-queried cleanup
- **AtmID Cache Reset**: Automatic platform cache invalidation based on change type
- **Optimistic Response**: Returns audio URLs for immediate frontend updates

### Webhook Payload (Answer Changes Only)
```json
{
  "mode": "welcome",
  "operation": "update",
  "conversation_id": "uuid",
  "chat_id": "client-uuid-1",
  "client_id": "client-id",
  "answer": "text content or null",
  "audio_url": "https://r2.url or null",
  "sector": "business",
  "lang": "en"
}
```

### Common Patterns
- **Single Record**: One intro per client with chat_id format `${clientId}-1`
- **PostgreSQL First**: Direct database operations with connection pooling
- **Cache Optimization**: Pass cached data from frontend to eliminate redundant queries
- **Change Flag Architecture**: Consistent with knowledgebase system
- **Conditional Operations**: Database updates and webhooks based on change flags
- **Comprehensive R2 Management**: Complete audio file lifecycle management

## User Interface

### Edit Mode Features
- **Edit Toggle**: Switch between view and edit modes
- **Change Detection**: Show cancel confirmation only if changes exist
- **Real-time Validation**: Instant feedback for audio codes and text
- **Success Feedback**: Auto-hide success overlay after 1.5 seconds

### Audio Mode Interface
- **Mode Toggle**: Switch between text and audio input
- **Debounced Validation**: 500ms delay for audio code validation
- **Visual Feedback**: Real-time validation status display
- **Code Reusability**: Allow reuse of existing audio codes

### State Management
- **Edit State**: Track editing mode and changes
- **Audio State**: Validation status and mode switching
- **Photo State**: Selection and search results
- **Success State**: Update feedback with auto-reset
- **Optimistic Updates**: Immediate UI updates for all change types

## Optimistic UI Updates

### Frontend State Synchronization
- **Photo Changes**: Updates `photo_url` and `photo_id` in local welcome data state
- **Answer Changes**: Updates `answer_p`, `audio_url`, `audio_file_path` based on mode
- **Mode Switching**: Properly clears text when audio mode, clears audio when text mode
- **Component State**: Syncs all relevant state variables for immediate UI feedback

### Update Flow
1. **User Action**: Photo selection, text edit, or audio recording
2. **Optimistic Update**: UI updates immediately with local data
3. **API Request**: Change flags sent to backend with relevant data only
4. **Database Update**: API handles database operations and R2 management
5. **State Sync**: Frontend state updated with server response data

## Implementation Status

### Core Features ✅
- **Change Flag Architecture**: Clean separation of frontend detection and API logic
- **Smart Webhooks**: Conditional firing based on answer changes only
- **Comprehensive R2 Management**: Dual cleanup mechanisms for complete file lifecycle
- **Optimistic UI Updates**: Immediate feedback for photo, text, and audio changes
- **Consistent Patterns**: Matches knowledgebase system architecture exactly
- **Automatic Cleanup**: AtmID cache invalidation and R2 file deletion handled by API

### Integration Points
- **Authentication**: User-scoped operations with `verifyAuth()`
- **Caching**: Dashboard cache for client data, fresh fetch for intro data
- **Storage**: Direct R2 uploads with comprehensive cleanup management
- **Database**: Single record per client with change flag-based updates
- **Knowledge System**: Shared photo search and consistent architecture patterns

### Key Patterns
1. **Change Flag System**: Use `photoChanged`/`answerChanged` boolean flags
2. **Conditional Operations**: Database updates and webhooks based on change flags
3. **Dual R2 Cleanup**: Frontend-provided path + database-queried file cleanup
4. **Optimistic Updates**: Immediate UI feedback with proper state synchronization
5. **Smart Webhooks**: Only fire for answer changes, not photo-only updates
6. **Timestamp Naming**: Use `${timestamp}-${uuid}` for audio file naming
7. **Automatic AtmID Reset**: Clear platform cache IDs based on change type
8. **Comprehensive Testing**: Test all scenarios (photo-only, text, audio, both)